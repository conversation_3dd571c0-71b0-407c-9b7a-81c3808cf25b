"use client"

import { useState } from "react"
import { ArrowLeft, Heart, MessageCircle, Settings, User, Bookmark, Clock, Zap, Menu } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import Link from "next/link"
import { useRouter } from "next/navigation"

const userData = {
  name: "张三",
  email: "<EMAIL>",
  avatar: "/user-profile-avatar.png",
  joinDate: "2024年1月",
  stats: {
    saves: 23,
    comments: 45,
    likes: 156,
  },
}

const savedResources = [
  {
    id: 1,
    title: "PDF转换大师",
    description: "在线PDF格式转换工具",
    category: "在线工具",
    thumbnail: "/pdf-converter-thumbnail.png",
    savedDate: "2024-01-15",
    gradient: "from-cyan-400 to-blue-500",
  },
  {
    id: 2,
    title: "思维导图制作",
    description: "专业的在线思维导图制作工具",
    category: "办公",
    thumbnail: "/placeholder-kj6u6.png",
    savedDate: "2024-01-12",
    gradient: "from-emerald-400 to-teal-500",
  },
  {
    id: 3,
    title: "代码格式化工具",
    description: "支持多种编程语言的代码美化工具",
    category: "在线工具",
    thumbnail: "/code-formatter-thumbnail.png",
    savedDate: "2024-01-10",
    gradient: "from-cyan-400 to-blue-500",
  },
]

const userComments = [
  {
    id: 1,
    resourceTitle: "PDF转换大师",
    resourceId: 1,
    content: "这个工具真的很好用，转换速度很快，质量也不错！界面设计也很简洁。",
    date: "2024-01-15",
    likes: 12,
  },
  {
    id: 2,
    resourceTitle: "思维导图制作",
    resourceId: 2,
    content: "界面简洁，操作简单，推荐给大家使用。特别是协作功能很实用。",
    date: "2024-01-12",
    likes: 8,
  },
  {
    id: 3,
    resourceTitle: "创意绘画板",
    resourceId: 4,
    content: "很有趣的工具，可以激发创造力。功能丰富，体验很好。",
    date: "2024-01-10",
    likes: 15,
  },
]

export default function ProfilePage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("saves")
  const [isEditing, setIsEditing] = useState(false)
  const [editForm, setEditForm] = useState({
    name: userData.name,
    email: userData.email,
  })

  const handleSaveProfile = () => {
    console.log("保存用户信息:", editForm)
    setIsEditing(false)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 科技感背景装饰 */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-cyan-400/20 to-blue-600/20 rounded-full blur-3xl"></div>
      </div>

      {/* 导航栏 */}
      <header className="relative bg-white/80 backdrop-blur-xl border-b border-gray-200/50">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            <div className="flex items-center space-x-6">
              <Button
                variant="ghost"
                onClick={() => router.back()}
                className="h-12 px-4 rounded-2xl hover:bg-gray-100/80"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                返回
              </Button>
              {/* 移动端菜单按钮 */}
              <Button variant="ghost" className="lg:hidden w-12 h-12 rounded-2xl hover:bg-gray-100/80">
                <Menu className="w-5 h-5" />
              </Button>
              <Link href="/" className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                  <Zap className="w-6 h-6 text-white" />
                </div>
                <span className="text-xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent">
                  FORMAT123
                </span>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* 主内容区域 */}
      <main className="relative max-w-7xl mx-auto px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* 左侧用户信息 */}
          <div className="lg:col-span-1">
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl shadow-gray-200/50 rounded-3xl overflow-hidden">
              <CardContent className="p-0">
                {/* 用户头像区域 */}
                <div className="relative p-8 bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-600 text-white text-center">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-purple-600/20"></div>
                  <Avatar className="relative w-24 h-24 mx-auto mb-4 border-4 border-white/30 shadow-xl">
                    <AvatarImage src={userData.avatar || "/placeholder.svg"} alt={userData.name} />
                    <AvatarFallback className="text-2xl bg-gradient-to-br from-blue-600 to-purple-700 text-white">
                      {userData.name[0]}
                    </AvatarFallback>
                  </Avatar>

                  <h2 className="text-2xl font-bold mb-2">{userData.name}</h2>
                  <p className="text-blue-100 mb-1">{userData.email}</p>
                  <p className="text-blue-200 text-sm">加入于 {userData.joinDate}</p>
                </div>

                {/* 统计信息 */}
                <div className="p-6">
                  <div className="grid grid-cols-3 gap-4 mb-6">
                    <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-cyan-50 rounded-2xl">
                      <div className="text-2xl font-bold text-blue-600 mb-1">{userData.stats.saves}</div>
                      <div className="text-xs text-gray-600 font-medium">收藏</div>
                    </div>
                    <div className="text-center p-4 bg-gradient-to-br from-emerald-50 to-teal-50 rounded-2xl">
                      <div className="text-2xl font-bold text-emerald-600 mb-1">{userData.stats.comments}</div>
                      <div className="text-xs text-gray-600 font-medium">评论</div>
                    </div>
                    <div className="text-center p-4 bg-gradient-to-br from-red-50 to-pink-50 rounded-2xl">
                      <div className="text-2xl font-bold text-red-600 mb-1">{userData.stats.likes}</div>
                      <div className="text-xs text-gray-600 font-medium">获赞</div>
                    </div>
                  </div>

                  <Button className="w-full h-12 rounded-2xl bg-gradient-to-r from-gray-100 to-gray-50 text-gray-700 hover:from-gray-200 hover:to-gray-100 border border-gray-200/50">
                    <Settings className="w-4 h-4 mr-2" />
                    编辑资料
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右侧主要内容 */}
          <div className="lg:col-span-3">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3 bg-white/80 backdrop-blur-sm border-0 shadow-lg shadow-gray-200/50 rounded-2xl p-2 h-14">
                <TabsTrigger
                  value="saves"
                  className="flex items-center h-10 rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-600 data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-blue-500/25 font-medium"
                >
                  <Bookmark className="w-4 h-4 mr-2" />
                  我的收藏
                </TabsTrigger>
                <TabsTrigger
                  value="comments"
                  className="flex items-center h-10 rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-emerald-500 data-[state=active]:to-teal-600 data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-emerald-500/25 font-medium"
                >
                  <MessageCircle className="w-4 h-4 mr-2" />
                  评论记录
                </TabsTrigger>
                <TabsTrigger
                  value="settings"
                  className="flex items-center h-10 rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-500 data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-orange-500/25 font-medium"
                >
                  <User className="w-4 h-4 mr-2" />
                  账号设置
                </TabsTrigger>
              </TabsList>

              {/* 我的收藏 */}
              <TabsContent value="saves" className="space-y-8 mt-8">
                <div className="flex justify-between items-center">
                  <h3 className="text-2xl font-bold text-gray-900">我的收藏 ({savedResources.length})</h3>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="rounded-2xl border-gray-200/50 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200 bg-transparent"
                    >
                      全部
                    </Button>
                    <Button variant="ghost" size="sm" className="rounded-2xl hover:bg-gray-100/80">
                      在线工具
                    </Button>
                    <Button variant="ghost" size="sm" className="rounded-2xl hover:bg-gray-100/80">
                      办公
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                  {savedResources.map((resource) => (
                    <Card
                      key={resource.id}
                      className="group overflow-hidden bg-white/80 backdrop-blur-sm border-0 shadow-lg shadow-gray-200/50 hover:shadow-xl hover:shadow-gray-300/50 transition-all duration-500 hover:-translate-y-2 rounded-3xl"
                    >
                      <CardContent className="p-0">
                        <div className="relative">
                          <img
                            src={resource.thumbnail || "/placeholder.svg"}
                            alt={resource.title}
                            className="w-full h-40 object-cover transition-transform duration-500 group-hover:scale-110"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                          <div
                            className={`absolute bottom-3 left-3 px-3 py-1.5 bg-gradient-to-r ${resource.gradient} text-white rounded-full text-xs font-medium shadow-lg`}
                          >
                            {resource.category}
                          </div>
                        </div>
                        <div className="p-6">
                          <Link href={`/resource/${resource.id}`}>
                            <h4 className="font-bold text-lg mb-2 text-gray-900 group-hover:bg-gradient-to-r group-hover:from-blue-600 group-hover:to-purple-600 group-hover:bg-clip-text group-hover:text-transparent transition-all duration-300 line-clamp-1">
                              {resource.title}
                            </h4>
                          </Link>

                          <p className="text-gray-600 text-sm mb-4 line-clamp-2 leading-relaxed">
                            {resource.description}
                          </p>

                          <div className="flex justify-between items-center text-xs text-gray-500">
                            <div className="flex items-center space-x-1">
                              <Clock className="w-3 h-3" />
                              <span>{resource.savedDate}</span>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-auto p-0 text-red-500 hover:text-red-600 font-medium"
                            >
                              取消收藏
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {savedResources.length === 0 && (
                  <div className="text-center py-20">
                    <div className="w-32 h-32 mx-auto mb-8 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center">
                      <Bookmark className="w-16 h-16 text-gray-400" />
                    </div>
                    <h3 className="text-2xl font-bold mb-4 bg-gradient-to-r from-gray-600 to-gray-800 bg-clip-text text-transparent">
                      还没有收藏
                    </h3>
                    <p className="text-gray-500 text-lg mb-6">去发现一些有用的资源吧</p>
                    <Button
                      asChild
                      className="px-8 py-3 rounded-2xl bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg shadow-blue-500/25"
                    >
                      <Link href="/">浏览资源</Link>
                    </Button>
                  </div>
                )}
              </TabsContent>

              {/* 评论记录 */}
              <TabsContent value="comments" className="space-y-8 mt-8">
                <div className="flex justify-between items-center">
                  <h3 className="text-2xl font-bold text-gray-900">评论记录 ({userComments.length})</h3>
                </div>

                <div className="space-y-6">
                  {userComments.map((comment) => (
                    <Card
                      key={comment.id}
                      className="bg-white/80 backdrop-blur-sm border-0 shadow-lg shadow-gray-200/50 rounded-3xl"
                    >
                      <CardContent className="p-6">
                        <div className="flex justify-between items-start mb-4">
                          <Link
                            href={`/resource/${comment.resourceId}`}
                            className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent hover:from-blue-700 hover:to-purple-700 transition-all duration-300"
                          >
                            {comment.resourceTitle}
                          </Link>
                          <span className="text-sm text-gray-500 bg-gray-100/80 px-3 py-1 rounded-full">
                            {comment.date}
                          </span>
                        </div>
                        <p className="text-gray-700 mb-4 leading-relaxed text-lg">{comment.content}</p>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center text-sm text-gray-500">
                            <Heart className="w-4 h-4 mr-2 text-red-500" />
                            <span className="font-medium">{comment.likes} 人点赞</span>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            className="rounded-2xl border-gray-200/50 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200 bg-transparent"
                            asChild
                          >
                            <Link href={`/resource/${comment.resourceId}`}>查看详情</Link>
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {userComments.length === 0 && (
                  <div className="text-center py-20">
                    <div className="w-32 h-32 mx-auto mb-8 bg-gradient-to-br from-emerald-100 to-teal-100 rounded-full flex items-center justify-center">
                      <MessageCircle className="w-16 h-16 text-gray-400" />
                    </div>
                    <h3 className="text-2xl font-bold mb-4 bg-gradient-to-r from-gray-600 to-gray-800 bg-clip-text text-transparent">
                      还没有评论
                    </h3>
                    <p className="text-gray-500 text-lg">参与讨论，分享你的想法</p>
                  </div>
                )}
              </TabsContent>

              {/* 账号设置 */}
              <TabsContent value="settings" className="space-y-8 mt-8">
                <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl shadow-gray-200/50 rounded-3xl">
                  <CardHeader>
                    <CardTitle className="text-2xl font-bold text-gray-900">基本信息</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {!isEditing ? (
                      <>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="p-6 bg-gradient-to-br from-blue-50 to-cyan-50 rounded-2xl">
                            <Label className="text-sm font-semibold text-gray-600 mb-2 block">用户名</Label>
                            <p className="text-lg font-medium text-gray-900">{userData.name}</p>
                          </div>
                          <div className="p-6 bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl">
                            <Label className="text-sm font-semibold text-gray-600 mb-2 block">邮箱</Label>
                            <p className="text-lg font-medium text-gray-900">{userData.email}</p>
                          </div>
                        </div>
                        <Button
                          onClick={() => setIsEditing(true)}
                          className="px-8 py-3 rounded-2xl bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg shadow-blue-500/25"
                        >
                          编辑信息
                        </Button>
                      </>
                    ) : (
                      <>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <Label htmlFor="name" className="text-sm font-semibold text-gray-700 mb-2 block">
                              用户名
                            </Label>
                            <Input
                              id="name"
                              value={editForm.name}
                              onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                              className="h-12 bg-white/80 border-gray-200/50 rounded-2xl focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/50"
                            />
                          </div>
                          <div>
                            <Label htmlFor="email" className="text-sm font-semibold text-gray-700 mb-2 block">
                              邮箱
                            </Label>
                            <Input
                              id="email"
                              type="email"
                              value={editForm.email}
                              onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}
                              className="h-12 bg-white/80 border-gray-200/50 rounded-2xl focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/50"
                            />
                          </div>
                        </div>
                        <div className="flex space-x-4">
                          <Button
                            onClick={handleSaveProfile}
                            className="px-8 py-3 rounded-2xl bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white shadow-lg shadow-emerald-500/25"
                          >
                            保存
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => setIsEditing(false)}
                            className="px-8 py-3 rounded-2xl border-gray-200/50 hover:bg-gray-50/80"
                          >
                            取消
                          </Button>
                        </div>
                      </>
                    )}
                  </CardContent>
                </Card>

                <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl shadow-gray-200/50 rounded-3xl">
                  <CardHeader>
                    <CardTitle className="text-2xl font-bold text-gray-900">偏好设置</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="flex items-center justify-between p-6 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-2xl">
                      <div>
                        <Label className="text-lg font-semibold text-gray-900 mb-1 block">邮件通知</Label>
                        <p className="text-sm text-gray-600">接收新评论和点赞通知</p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="px-6 py-2 rounded-2xl border-gray-200/50 hover:bg-emerald-50 hover:text-emerald-600 hover:border-emerald-200 bg-transparent"
                      >
                        开启
                      </Button>
                    </div>
                    <div className="flex items-center justify-between p-6 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-2xl">
                      <div>
                        <Label className="text-lg font-semibold text-gray-900 mb-1 block">隐私设置</Label>
                        <p className="text-sm text-gray-600">设置个人资料的可见性</p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="px-6 py-2 rounded-2xl border-gray-200/50 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200 bg-transparent"
                      >
                        公开
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl shadow-gray-200/50 rounded-3xl">
                  <CardHeader>
                    <CardTitle className="text-2xl font-bold text-red-600">危险操作</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="p-6 bg-gradient-to-r from-red-50 to-pink-50 rounded-2xl">
                      <Label className="text-lg font-semibold text-gray-900 mb-2 block">删除账号</Label>
                      <p className="text-sm text-gray-600 mb-4">删除账号后，所有数据将无法恢复</p>
                      <Button
                        variant="destructive"
                        size="sm"
                        className="px-6 py-2 rounded-2xl bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 shadow-lg shadow-red-500/25"
                      >
                        删除账号
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>
    </div>
  )
}
