"use client"

import { useState } from "react"
import {
  ArrowLeft,
  Heart,
  ThumbsDown,
  MessageCircle,
  Star,
  ExternalLink,
  Share2,
  Flag,
  Zap,
  TrendingUp,
  Menu,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import Link from "next/link"
import { useRouter } from "next/navigation"

const resourceData = {
  id: 1,
  title: "PDF转换大师",
  description:
    "这是一款功能强大的在线PDF转换工具，支持PDF与Word、Excel、PowerPoint、图片等多种格式之间的相互转换。界面简洁易用，转换速度快，支持批量处理，是办公和学习的必备工具。无需安装任何软件，完全基于云端处理，保护您的隐私安全。",
  category: "在线工具",
  thumbnail: "/pdf-converter-interface.png",
  url: "https://example.com/pdf-converter",
  likes: 1234,
  dislikes: 12,
  comments: 89,
  saves: 456,
  rating: 4.8,
  tags: ["PDF转换", "办公工具", "文档处理", "在线工具", "批量处理"],
  features: [
    "支持20+种格式相互转换",
    "批量处理，提升工作效率",
    "高质量转换，保持原始格式",
    "无需安装软件，在线即用",
    "云端处理，保护隐私安全",
    "支持大文件上传处理",
  ],
}

const mockComments = [
  {
    id: 1,
    user: "张三",
    avatar: "/user-avatar-1.png",
    content: "这个工具真的很好用，转换速度很快，质量也不错！界面设计也很简洁，用起来很舒服。",
    time: "2小时前",
    likes: 12,
  },
  {
    id: 2,
    user: "李四",
    avatar: "/diverse-user-avatar-set-2.png",
    content: "界面简洁，操作简单，推荐给大家使用。特别是批量转换功能很实用。",
    time: "5小时前",
    likes: 8,
  },
  {
    id: 3,
    user: "王五",
    avatar: "/diverse-user-avatars-3.png",
    content: "免费版功能就很够用了，比其他同类工具好用多了。转换质量很高。",
    time: "1天前",
    likes: 15,
  },
]

export default function ResourceDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [showAllComments, setShowAllComments] = useState(false)
  const [newComment, setNewComment] = useState("")
  const [isLiked, setIsLiked] = useState(false)
  const [isDisliked, setIsDisliked] = useState(false)
  const [isSaved, setIsSaved] = useState(false)

  const handleInteraction = (action: string) => {
    if (!isLoggedIn) {
      alert("登录后可操作")
      return
    }

    switch (action) {
      case "like":
        setIsLiked(!isLiked)
        setIsDisliked(false)
        break
      case "dislike":
        setIsDisliked(!isDisliked)
        setIsLiked(false)
        break
      case "save":
        setIsSaved(!isSaved)
        break
    }
  }

  const handleCommentSubmit = () => {
    if (!isLoggedIn) {
      alert("登录后可评论")
      return
    }
    if (newComment.trim()) {
      console.log("提交评论:", newComment)
      setNewComment("")
    }
  }

  const displayedComments = showAllComments ? mockComments : mockComments.slice(0, 3)

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 科技感背景装饰 */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-cyan-400/20 to-blue-600/20 rounded-full blur-3xl"></div>
      </div>

      {/* 导航栏 */}
      <header className="relative bg-white/80 backdrop-blur-xl border-b border-gray-200/50">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            <div className="flex items-center space-x-6">
              <Button
                variant="ghost"
                onClick={() => router.back()}
                className="h-12 px-4 rounded-2xl hover:bg-gray-100/80"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                返回
              </Button>
              {/* 移动端菜单按钮 */}
              <Button variant="ghost" className="lg:hidden w-12 h-12 rounded-2xl hover:bg-gray-100/80">
                <Menu className="w-5 h-5" />
              </Button>
              <Link href="/" className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                  <Zap className="w-6 h-6 text-white" />
                </div>
                <span className="text-xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent">
                  FORMAT123
                </span>
              </Link>
            </div>

            {!isLoggedIn ? (
              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  onClick={() => setIsLoggedIn(true)}
                  className="h-12 px-6 rounded-2xl border-gray-200/50 hover:bg-gray-50/80"
                >
                  登录
                </Button>
                <Button
                  onClick={() => setIsLoggedIn(true)}
                  className="h-12 px-6 rounded-2xl bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-lg shadow-orange-500/25"
                >
                  注册
                </Button>
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-600">欢迎回来！</span>
                <Button
                  variant="outline"
                  onClick={() => setIsLoggedIn(false)}
                  className="h-12 px-6 rounded-2xl border-gray-200/50 hover:bg-gray-50/80"
                >
                  退出
                </Button>
              </div>
            )}
          </div>
        </div>
      </header>

      {/* 主内容区域 */}
      <main className="relative max-w-7xl mx-auto px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* 左侧主要内容 */}
          <div className="lg:col-span-2 space-y-8">
            {/* 资源信息卡片 */}
            <Card className="overflow-hidden bg-white/80 backdrop-blur-sm border-0 shadow-xl shadow-gray-200/50 rounded-3xl">
              <CardContent className="p-0">
                <div className="relative">
                  {/* 大图预览 */}
                  <div className="relative h-80 overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-cyan-50 to-blue-50 opacity-50"></div>
                    <img
                      src={resourceData.thumbnail || "/placeholder.svg"}
                      alt={resourceData.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>

                    {/* 浮动信息 */}
                    <div className="absolute bottom-6 left-6 right-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center space-x-1 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full">
                            <Star className="w-4 h-4 text-yellow-500 fill-current" />
                            <span className="font-semibold text-gray-800">{resourceData.rating}</span>
                          </div>
                          <div className="flex items-center space-x-1 bg-gradient-to-r from-red-500 to-orange-500 text-white px-4 py-2 rounded-full">
                            <TrendingUp className="w-4 h-4" />
                            <span className="font-medium text-sm">HOT</span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-1 bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-4 py-2 rounded-full">
                          <Zap className="w-4 h-4" />
                          <span className="font-medium text-sm">{resourceData.category}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 内容信息 */}
                  <div className="p-8">
                    <div className="mb-6">
                      <h1 className="text-3xl font-bold mb-4 bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                        {resourceData.title}
                      </h1>
                      <div className="flex items-center space-x-6 text-sm text-gray-600 mb-6">
                        <div className="flex items-center space-x-1">
                          <Heart className="w-4 h-4" />
                          <span className="font-medium">{resourceData.likes}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MessageCircle className="w-4 h-4" />
                          <span className="font-medium">{resourceData.comments}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Star className="w-4 h-4" />
                          <span className="font-medium">{resourceData.saves}</span>
                        </div>
                      </div>
                    </div>

                    <p className="text-gray-700 leading-relaxed mb-8 text-lg">{resourceData.description}</p>

                    {/* 功能特点 */}
                    <div className="mb-8">
                      <h3 className="text-xl font-bold mb-4 text-gray-900">功能特点</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {resourceData.features.map((feature, index) => (
                          <div
                            key={index}
                            className="flex items-center space-x-3 p-4 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-2xl"
                          >
                            <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full"></div>
                            <span className="text-gray-700 font-medium">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* 标签 */}
                    <div className="flex flex-wrap gap-3">
                      {resourceData.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="px-4 py-2 bg-gradient-to-r from-gray-100 to-gray-50 text-gray-700 rounded-2xl font-medium text-sm border border-gray-200/50"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 评论区域 */}
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl shadow-gray-200/50 rounded-3xl">
              <CardHeader className="pb-6">
                <CardTitle className="flex items-center text-2xl font-bold text-gray-900">
                  <MessageCircle className="w-6 h-6 mr-3 text-blue-500" />
                  评论讨论 ({mockComments.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* 评论输入框 */}
                {isLoggedIn ? (
                  <div className="space-y-4">
                    <Textarea
                      placeholder="分享你的使用体验..."
                      value={newComment}
                      onChange={(e) => setNewComment(e.target.value)}
                      className="min-h-[120px] bg-white/80 border-gray-200/50 rounded-2xl resize-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/50"
                    />
                    <div className="flex justify-end">
                      <Button
                        onClick={handleCommentSubmit}
                        disabled={!newComment.trim()}
                        className="px-8 py-3 rounded-2xl bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg shadow-blue-500/25"
                      >
                        发布评论
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 text-center">
                    <MessageCircle className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                    <p className="text-gray-600 mb-4 text-lg">登录后参与讨论</p>
                    <Button
                      onClick={() => setIsLoggedIn(true)}
                      className="px-8 py-3 rounded-2xl bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg shadow-blue-500/25"
                    >
                      立即登录
                    </Button>
                  </div>
                )}

                <Separator className="bg-gray-200/50" />

                {/* 评论列表 */}
                <div className="space-y-6">
                  {displayedComments.map((comment) => (
                    <div
                      key={comment.id}
                      className="flex space-x-4 p-6 bg-gradient-to-r from-gray-50/50 to-blue-50/30 rounded-2xl"
                    >
                      <Avatar className="w-12 h-12 border-2 border-white shadow-lg">
                        <AvatarImage src={comment.avatar || "/placeholder.svg"} alt={comment.user} />
                        <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold">
                          {comment.user[0]}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <span className="font-semibold text-gray-900">{comment.user}</span>
                          <span className="text-sm text-gray-500">{comment.time}</span>
                        </div>
                        <p className="text-gray-700 mb-3 leading-relaxed">{comment.content}</p>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 px-3 rounded-xl hover:bg-red-50 hover:text-red-500 transition-colors"
                        >
                          <Heart className="w-4 h-4 mr-1" />
                          <span className="font-medium">{comment.likes}</span>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>

                {mockComments.length > 3 && (
                  <div className="text-center pt-4">
                    <Button
                      variant="outline"
                      onClick={() => setShowAllComments(!showAllComments)}
                      className="px-8 py-3 rounded-2xl border-gray-200/50 hover:bg-gray-50/80"
                    >
                      {showAllComments ? "收起评论" : `查看全部 ${mockComments.length} 条评论`}
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* 右侧操作栏 */}
          <div className="space-y-8">
            {/* 主要操作 */}
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl shadow-gray-200/50 rounded-3xl">
              <CardContent className="p-8 space-y-6">
                <Button
                  className="w-full h-14 text-lg font-semibold bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-2xl shadow-lg shadow-blue-500/25 transition-all duration-300"
                  asChild
                >
                  <a href={resourceData.url} target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="w-5 h-5 mr-3" />
                    立即体验
                  </a>
                </Button>

                <div className="grid grid-cols-3 gap-3">
                  <Button
                    variant="outline"
                    onClick={() => handleInteraction("like")}
                    className={`h-12 rounded-2xl border-gray-200/50 transition-all duration-300 ${
                      !isLoggedIn ? "opacity-50" : ""
                    } ${
                      isLiked
                        ? "bg-gradient-to-r from-red-500 to-pink-500 text-white border-transparent shadow-lg shadow-red-500/25"
                        : "hover:bg-red-50 hover:text-red-500 hover:border-red-200"
                    }`}
                  >
                    <Heart className={`w-5 h-5 ${isLiked ? "fill-current" : ""}`} />
                  </Button>

                  <Button
                    variant="outline"
                    onClick={() => handleInteraction("dislike")}
                    className={`h-12 rounded-2xl border-gray-200/50 transition-all duration-300 ${
                      !isLoggedIn ? "opacity-50" : ""
                    } ${
                      isDisliked
                        ? "bg-gradient-to-r from-gray-500 to-gray-600 text-white border-transparent shadow-lg shadow-gray-500/25"
                        : "hover:bg-gray-50 hover:text-gray-600 hover:border-gray-300"
                    }`}
                  >
                    <ThumbsDown className={`w-5 h-5 ${isDisliked ? "fill-current" : ""}`} />
                  </Button>

                  <Button
                    variant="outline"
                    onClick={() => handleInteraction("save")}
                    className={`h-12 rounded-2xl border-gray-200/50 transition-all duration-300 ${
                      !isLoggedIn ? "opacity-50" : ""
                    } ${
                      isSaved
                        ? "bg-gradient-to-r from-yellow-500 to-orange-500 text-white border-transparent shadow-lg shadow-yellow-500/25"
                        : "hover:bg-yellow-50 hover:text-yellow-600 hover:border-yellow-200"
                    }`}
                  >
                    <Star className={`w-5 h-5 ${isSaved ? "fill-current" : ""}`} />
                  </Button>
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <Button
                    variant="outline"
                    className="h-12 rounded-2xl border-gray-200/50 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200 bg-transparent"
                  >
                    <Share2 className="w-4 h-4 mr-2" />
                    分享
                  </Button>
                  <Button
                    variant="outline"
                    className="h-12 rounded-2xl border-gray-200/50 hover:bg-red-50 hover:text-red-600 hover:border-red-200 bg-transparent"
                  >
                    <Flag className="w-4 h-4 mr-2" />
                    举报
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 统计信息 */}
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl shadow-gray-200/50 rounded-3xl">
              <CardHeader>
                <CardTitle className="text-xl font-bold text-gray-900">数据统计</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center p-4 bg-gradient-to-r from-red-50 to-pink-50 rounded-2xl">
                  <span className="text-gray-700 font-medium">点赞数</span>
                  <span className="text-xl font-bold text-red-600">{resourceData.likes}</span>
                </div>
                <div className="flex justify-between items-center p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-2xl">
                  <span className="text-gray-700 font-medium">收藏数</span>
                  <span className="text-xl font-bold text-orange-600">{resourceData.saves}</span>
                </div>
                <div className="flex justify-between items-center p-4 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-2xl">
                  <span className="text-gray-700 font-medium">评论数</span>
                  <span className="text-xl font-bold text-blue-600">{resourceData.comments}</span>
                </div>
                <div className="flex justify-between items-center p-4 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-2xl">
                  <span className="text-gray-700 font-medium">综合评分</span>
                  <div className="flex items-center space-x-1">
                    <Star className="w-5 h-5 text-yellow-500 fill-current" />
                    <span className="text-xl font-bold text-purple-600">{resourceData.rating}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}
